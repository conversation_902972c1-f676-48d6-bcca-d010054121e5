namespace TimeLens.Client.Tasks;

using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using Coravel.Invocable;
using Services;
using Models;
using Microsoft.Extensions.Logging;
using Refit;
using Utils;

// {"stocks":[{"market":"hk","symbol":"01810","key":"小米"}],"needPushWB":false,"needCMSLink":true,"needCalender":false}

public class Stock 
{
    public string Market { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
}
public class Ext 
{
    public List<Stock> Stocks { get; set; }
    public bool NeedPushWB { get; set; }
    public bool NeedCMSLink { get; set; }
    public bool NeedCalender { get; set; }
    public string Docurl { get; set; } = string.Empty;
    public string Docid { get; set; } = string.Empty;
}

public partial class FinanceSinaNewsTask(
    ISinaFeedApi sinaFeedApi,
    NewsService newsService,
    ILogger<FinanceSinaNewsTask> logger) : IInvocable {

    public async Task Invoke()
    {
        try
        {
            logger.LogInformation("开始执行新浪财经新闻获取任务");

            var sinaFeedParams = new SinaFeedParams
            {
                Page = 1,
                PageSize = 20,
                ZhiboId = 152,
                TagId = 0,
                Dire = "f",
                Dpc = 1,
                Timestamp = DateTimeOffset.Now.ToUnixTimeMilliseconds()
            };

            var sinaFeedResult = await sinaFeedApi.GetTotalSinaFeed(sinaFeedParams);

            if (sinaFeedResult?.Result?.Data?.Feed?.List != null)
            {
                var consumeCount = await ConvertSinaFeedToNewsItems(sinaFeedResult.Result.Data.Feed.List);
                logger.LogInformation("成功获取并更新了 {Count} 条新闻", consumeCount);
            }
            else
            {
                logger.LogWarning("新浪财经 API 返回空数据");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "执行新浪财经新闻获取任务时发生错误");
        }
    }

    private async ValueTask<int> ConvertSinaFeedToNewsItems(List<SinaFeedNewsItem> sinaItems) {
        var consumeCount = 0;
        foreach (var sinaItem in sinaItems)
        {
            try
            {
                var importanceAssessment = await AITool.ImportantNews(sinaItem.RichText);
                var importanceStatus = TransferImportance(importanceAssessment.Assessment);
                if (importanceStatus == ImportanceCategory.Low)
                    continue;
                var stocks = ExtractStocks(sinaItem.Ext);
                string company;
                if (stocks.Count == 0)
                {
                    company = await AITool.ExtractCompany(sinaItem.RichText);
                    if (company.Trim().Equals("无"))
                    {
                        company = string.Empty;
                    }
                }
                else
                {
                    company = stocks.First();
                }
                
                var conceptThemes = await ExtractConceptThemes(sinaItem.RichText);
                var tags = await AITool.ExtractTags(sinaItem.Ext);
                var newsItem = new NewsItem
                {
                    Title = ExtractTitle(sinaItem.RichText),
                    Content = ExtractContent(sinaItem.RichText),
                    PublishDate = DateTime.ParseExact(sinaItem.CreateTime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                    Company =  company,
                    Tags = tags.Tags,
                    Category = DetermineNewsCategory(sinaItem.RichText),
                    Summary = "",
                    Source = "新浪财经",
                    Importance = importanceStatus,
                    ConceptThemes = conceptThemes
                };
                await newsService.AppendNewsItemsAsync(newsItem, sinaItem.Id.ToString(), SiteCategory.Sina, conceptThemes).ConfigureAwait(false);
                Console.WriteLine($"新浪财经新闻项转换成功，ID: {sinaItem.Id} >>> TO: {newsItem.Title}");
                consumeCount++;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "转换新浪财经新闻项时发生错误，ID: {Id}", sinaItem.Id);
            }
        }
        return consumeCount;
    }
    
    private string ExtractTitle(string richText) {
        // 使用正则表达式匹配【】中的内容
        var match = TitleRegex().Match(richText);
        return !match.Success ? richText : match.Groups[1].Value;
    }
    
    private string ExtractContent(string richText) {
        // 使用正则表达式匹配【】中的内容
        var match = ContentRegex().Match(richText);
        return !match.Success ? richText : match.Groups[1].Value;
    }

    private ImportanceCategory TransferImportance(string importanceText) {
        if (string.IsNullOrWhiteSpace(importanceText))
            return ImportanceCategory.Low;
        var status = importanceText switch
        {
            "重要" => ImportanceCategory.High,
            "一般" => ImportanceCategory.Normal,
            _ => ImportanceCategory.Low,
        };
        return status;
    }

    private List<string> ExtractStocks(string ext) 
    {
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        options.Converters.Add(new ObjectToInferredTypesConverter());
        options.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase));
        var extObj = JsonSerializer.Deserialize<Ext>(ext, options);
        return extObj.Stocks.Where(it => !it.Market.Equals("foreign"))
            .Select(it => $"{it.Key}({it.Symbol})").ToList();
    }

    private NewsCategory DetermineNewsCategory(string richText)
    {
        if (richText.Contains("年报") || richText.Contains("年度报告"))
            return NewsCategory.年报;
        if (richText.Contains("季报") || richText.Contains("季度报告"))
            return NewsCategory.季报;
        if (richText.Contains("分红") || richText.Contains("派息"))
            return NewsCategory.分红;
        if (richText.Contains("增发") || richText.Contains("配股"))
            return NewsCategory.增发;
        if (richText.Contains("重组") || richText.Contains("并购"))
            return NewsCategory.重组;
        if (richText.Contains("股东大会"))
            return NewsCategory.股东大会;
        return richText.Contains("投资者关系") ? NewsCategory.投资者关系 : NewsCategory.其他;
    }
    

    private async Task<List<ConceptTheme>> ExtractConceptThemes(string richText) {
        var tags = await AITool.SectorOrConcept(richText);
        var conceptThemes = new List<ConceptTheme>();
        tags.Sector.ForEach(it => conceptThemes.Add(
            new ConceptTheme
            {
                Name = it, 
                Heat = Random.Shared.Next(50, 100), 
                ActiveStocks = []
            }));
        tags.Concept.ForEach(it => conceptThemes.Add(
            new ConceptTheme
            {
                Name = it, Heat = Random.Shared.Next(50, 100), ActiveStocks = []
            }));
        return conceptThemes;
    }

    [GeneratedRegex(@"【(.*?)】")]
    private static partial Regex TitleRegex();
    
    [GeneratedRegex(@"【.*】(.*?)")]
    private static partial Regex ContentRegex();
}
