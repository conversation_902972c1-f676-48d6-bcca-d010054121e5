using System.Text.Json;
using System.Text.Json.Serialization;
using Coravel;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Http;
using Refit;
using TimeLens.Client.Services;
using TimeLens.Client.Tasks;
using TimeLens.Client.Utils;
using TimeLens.Client.Data.Extensions;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Add Ant Design
builder.Services.AddAntDesign();

// Add Enhanced Data Access Services for WebAssembly
var baseAddress = builder.HostEnvironment.BaseAddress;
builder.Services.AddApiClientServices(baseAddress);

var options = new JsonSerializerOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
};
options.Converters.Add(new ObjectToInferredTypesConverter());
options.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase));
// Add HttpClient
builder.Services.AddRefitClient<ISinaFeedApi>(new RefitSettings()
    {
        ContentSerializer = new SystemTextJsonContentSerializer(options),
        UrlParameterKeyFormatter = new SnakeCaseUrlParameterKeyFormatter()
    })
    .ConfigureHttpClient(c => {
        c.BaseAddress = new Uri("https://zhibo.sina.com.cn");
        c.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        c.DefaultRequestHeaders.Referrer = new Uri("https://zhibo.sina.com.cn/");
        c.DefaultRequestHeaders.Host = "zhibo.sina.com.cn";
        c.Timeout = TimeSpan.FromSeconds(20);
    }).AddHttpMessageHandler(serviceProvider 
        => new HttpInterceptorLogger(serviceProvider.GetRequiredService<ILogger<HttpInterceptorLogger>>()));
builder.Services.AddScoped(sp => {
    var httpClient = new HttpClient();
    httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
    return httpClient;
});

// Add Services
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<HttpContextAccessor>();

// Set default culture
var culture = new System.Globalization.CultureInfo("zh-CN");
System.Globalization.CultureInfo.DefaultThreadCurrentCulture = culture;
System.Globalization.CultureInfo.DefaultThreadCurrentUICulture = culture;

// Configure Ant Design
// Theme configuration removed as DocTheme is not available in this version
builder.Services.AddSingleton<HttpInterceptorLogger>();
var app = builder.Build();
await app.RunAsync();
