using TimeLens.Client.Data.Attributes;

namespace TimeLens.Client.Models
{
    /// <summary>
    /// 各类站点的最新新闻同步记录状态
    /// </summary>
    [Table("latest_news_sync_states")]
    [GenerateRepository]
    public class LatestNewsSyncState
    {
        [Column("id", IsPrimaryKey = true, IsNullable = false)]
        public string Id { get; init; } = Ulid.NewUlid().ToString();
        [Column("sync_id", IsNullable = false)]
        public required string SyncId { get; set; }
        
        [Column("site", DataType = "INTEGER", IsNullable = false)]
        public SiteCategory Site { get; init; }
        [Column("created_at", IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at", IsNullable = false)]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 概念题材实体类
    /// </summary>
    [Table("concept_themes")]
    [GenerateRepository]
    public class ConceptTheme
    {
        [Column("id", IsPrimaryKey = true, IsNullable = false)]
        public string Id { get; init; } = Ulid.NewUlid().ToString();

        [Column("name", IsNullable = false)]
        public required string Name { get; set; } // 概念题材名称

        [Column("heat", IsNullable = false)]
        public int Heat { get; set; } // 热度

        [Column("active_stocks", DataType = "VARCHAR[]")]
        public List<string> ActiveStocks { get; init; } = []; // 活跃个股列表，使用 JSON 类型存储

        [Column("created_at", IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at", IsNullable = false)]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 新闻项目实体类
    /// </summary>
    [Table("news_items")]
    [GenerateRepository]
    public class NewsItem
    {
        [Column("id", IsPrimaryKey = true, IsNullable = false)]
        public string Id { get; init; } = Ulid.NewUlid().ToString();

        [Column("title", IsNullable = false)]
        public required string Title { get; init; }

        [Column("content", DataType = "TEXT", IsNullable = false)]
        public required string Content { get; init; }

        [Column("publish_date", IsNullable = false)]
        public DateTime PublishDate { get; init; }

        [Column("company", IsNullable = false)]
        public required string Company { get; init; }

        [Column("tags", DataType = "VARCHAR[]")]
        public List<string> Tags { get; init; } = [];

        [Column("category", DataType = "INTEGER", IsNullable = false)]
        public NewsCategory Category { get; init; }

        [Column("summary", DataType = "TEXT", IsNullable = false)]
        public required string Summary { get; init; }

        [Column("source")]
        public required string? Source { get; init; }
        
        [Column("importance")]
        public required ImportanceCategory Importance { get; init; }

        // ConceptThemes 通过关联表 NewsItemConceptTheme 实现多对多关系
        // 这个属性不映射到数据库列，而是通过 Repository 层的 JOIN 查询填充
        public List<ConceptTheme> ConceptThemes { get; init; } = [];

        [Column("created_at", IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at", IsNullable = false)]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    public enum NewsCategory
    {
        重组,  // 重组
        分红,       // 分红
        增发, // 增发
        年报,   // 年报
        季报, // 季报
        股东大会, // 股东大会
        投资者关系,   // 投资者关系
        其他          // 其他
    }
    
    public enum ImportanceCategory
    {
        High,
        Normal,
        Low
    }

    public enum SiteCategory
    {
        Sina, // 新浪财经
        EastMoney, // 东方财富
    }
}
