using TimeLens.Client.Data.Context;
using TimeLens.Client.Data.Repositories;
using TimeLens.Client.Services;

namespace TimeLens.Client.Data.Extensions
{
    /// <summary>
    /// 增强的服务注册扩展方法
    /// </summary>
    public static class EnhancedServiceCollectionExtensions
    {
        /// <summary>
        /// 添加DuckDB数据库服务（用于Blazor Server）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="connectionString">数据库连接字符串，默认使用内存数据库</param>
        /// <returns></returns>
        private static IServiceCollection AddDuckDbServerServices(this IServiceCollection services,
            string connectionString = "Data Source=:memory:")
        {
            // 注册数据库上下文
            services.AddScoped<IEnhancedDuckDbContext>(provider =>
            {
                var logger = provider.GetService<ILogger<EnhancedDuckDbContext>>();
                return new EnhancedDuckDbContext(connectionString, logger);
            });

            // 注册Repository (注意顺序：先注册被引用的表，再注册引用其他表的表)
            services.AddScoped<ILatestNewsSyncStateRepository, EnhancedLatestNewsSyncStateRepository>();
            services.AddScoped<IConceptThemeRepository, EnhancedConceptThemeRepository>();
            services.AddScoped<INewsItemRepository, EnhancedNewsItemRepository>();
            services.AddScoped<INewsItemConceptThemeRepository, NewsItemConceptThemeRepository>();

            // Register NewsService for server environment
            services.AddScoped<NewsService>(provider =>
            {
                var latestNewsSyncStateRepository = provider.GetRequiredService<ILatestNewsSyncStateRepository>();
                var conceptThemeRepository = provider.GetRequiredService<IConceptThemeRepository>();
                var newsItemRepository = provider.GetRequiredService<INewsItemRepository>();
                var newsItemConceptThemeRepository = provider.GetRequiredService<INewsItemConceptThemeRepository>();

                return new NewsService(latestNewsSyncStateRepository, conceptThemeRepository, newsItemRepository, newsItemConceptThemeRepository);
            });

            return services;
        }

        /// <summary>
        /// 添加HTTP API客户端服务（用于Blazor WebAssembly）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="baseAddress">API基础地址</param>
        /// <returns></returns>
        public static IServiceCollection AddApiClientServices(this IServiceCollection services,
            string? baseAddress = null)
        {
            // 配置HttpClient
            if (!string.IsNullOrEmpty(baseAddress))
            {
                services.AddHttpClient<INewsItemApiService, NewsItemApiService>(client =>
                {
                    client.BaseAddress = new Uri(baseAddress);
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                    client.Timeout = TimeSpan.FromSeconds(30);
                });

                services.AddHttpClient<IConceptThemeApiService, ConceptThemeApiService>(client =>
                {
                    client.BaseAddress = new Uri(baseAddress);
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                    client.Timeout = TimeSpan.FromSeconds(30);
                });

                services.AddHttpClient<ILatestNewsSyncStateApiService, LatestNewsSyncStateApiService>(client =>
                {
                    client.BaseAddress = new Uri(baseAddress);
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                    client.Timeout = TimeSpan.FromSeconds(30);
                });

                services.AddHttpClient<INewsItemConceptThemeApiService, NewsItemConceptThemeApiService>(client =>
                {
                    client.BaseAddress = new Uri(baseAddress);
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                    client.Timeout = TimeSpan.FromSeconds(30);
                });
            }
            else
            {
                services.AddScoped<INewsItemApiService, NewsItemApiService>();
                services.AddScoped<IConceptThemeApiService, ConceptThemeApiService>();
                services.AddScoped<ILatestNewsSyncStateApiService, LatestNewsSyncStateApiService>();
                services.AddScoped<INewsItemConceptThemeApiService, NewsItemConceptThemeApiService>();
            }

            // Register NewsService for WebAssembly environment
            services.AddScoped<NewsService>(provider =>
            {
                var latestNewsSyncStateApiService = provider.GetRequiredService<ILatestNewsSyncStateApiService>();
                var conceptThemeApiService = provider.GetRequiredService<IConceptThemeApiService>();
                var newsItemApiService = provider.GetRequiredService<INewsItemApiService>();
                var newsItemConceptThemeApiService = provider.GetRequiredService<INewsItemConceptThemeApiService>();

                return new NewsService(latestNewsSyncStateApiService, conceptThemeApiService, newsItemApiService, newsItemConceptThemeApiService);
            });

            return services;
        }

        /// <summary>
        /// 添加DuckDB文件数据库服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="databasePath">数据库文件路径</param>
        /// <returns></returns>
        private static IServiceCollection AddDuckDbFileServices(this IServiceCollection services, 
            string databasePath = "timelens.db")
        {
            var connectionString = $"Data Source={databasePath}";
            return services.AddDuckDbServerServices(connectionString);
        }

        /// <summary>
        /// 添加完整的数据访问层服务（自动检测环境）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="apiBaseAddress">API基础地址（WebAssembly环境使用）</param>
        /// <returns></returns>
        public static IServiceCollection AddDataAccessServices(this IServiceCollection services,
            string connectionString = "Data Source=:memory:",
            string? apiBaseAddress = null)
        {
            // 检测是否为WebAssembly环境
            var isWebAssembly = System.Runtime.InteropServices.RuntimeInformation.OSDescription.Contains("Browser");
            
            if (isWebAssembly)
            {
                // WebAssembly环境使用HTTP API客户端
                services.AddApiClientServices(apiBaseAddress);
            }
            else
            {
                // 服务器环境使用DuckDB
                services.AddDuckDbServerServices(connectionString);
            }

            return services;
        }

        /// <summary>
        /// 添加开发环境服务（内存数据库用于快速开发）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns></returns>
        public static IServiceCollection AddDevelopmentServices(this IServiceCollection services)
        {
            return services.AddDuckDbServerServices();
        }

        /// <summary>
        /// 添加生产环境服务（持久化数据库）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="databasePath">数据库文件路径</param>
        /// <returns></returns>
        public static IServiceCollection AddProductionServices(this IServiceCollection services,
            string databasePath = "production.db")
        {
            return services.AddDuckDbFileServices(databasePath);
        }

        /// <summary>
        /// 添加测试环境服务（内存数据库用于单元测试）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns></returns>
        public static IServiceCollection AddTestingServices(this IServiceCollection services)
        {
            return services.AddDuckDbServerServices();
        }
    }
}
