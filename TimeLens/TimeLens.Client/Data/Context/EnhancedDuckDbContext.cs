using DuckDB.NET.Data;
using System.Data;
using System.Data.Common;

namespace TimeLens.Client.Data.Context
{
    /// <summary>
    /// 增强的DuckDB数据库上下文接口
    /// </summary>
    public interface IEnhancedDuckDbContext : IDisposable
    {
        DuckDBConnection Connection { get; }
        bool HasActiveTransaction { get; }
        Task<DuckDBCommand> CreateCommandAsync(string sql);
        Task<T?> ExecuteScalarAsync<T>(string sql, params DuckDBParameter[] parameters);
        Task<int> ExecuteNonQueryAsync(string sql, params DuckDBParameter[] parameters);
        Task<DuckDBDataReader> ExecuteReaderAsync(string sql, params DuckDBParameter[] parameters);
        Task<IEnumerable<T>> QueryAsync<T>(string sql, Func<DbDataReader, T> mapper, params DuckDBParameter[] parameters);
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        Task<bool> TableExistsAsync(string tableName);
        Task CreateTableAsync(string createTableSql);
    }

    /// <summary>
    /// 增强的DuckDB数据库上下文实现
    /// </summary>
    public class EnhancedDuckDbContext(string connectionString, ILogger<EnhancedDuckDbContext>? logger = null)
        : IEnhancedDuckDbContext
    {
        private DuckDBConnection? _connection;
        private DuckDBTransaction? _transaction;
        private bool _disposed;

        public DuckDBConnection Connection
        {
            get
            {
                if (_connection is { State: ConnectionState.Open }) return _connection;
                _connection?.Dispose();
                _connection = new DuckDBConnection(connectionString);
                _connection.Open();
                logger?.LogDebug("DuckDB connection opened: {ConnectionString}", connectionString);
                return _connection;
            }
        }

        public Task<DuckDBCommand> CreateCommandAsync(string sql)
        {
            var command = Connection.CreateCommand();
            command.CommandText = sql;
            if (_transaction != null)
            {
                command.Transaction = _transaction;
            }
            return Task.FromResult(command);
        }

        public async Task<T?> ExecuteScalarAsync<T>(string sql, params DuckDBParameter[] parameters)
        {
            try
            {
                await using var command = await CreateCommandAsync(sql);
                if (parameters.Length > 0)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                var result = await command.ExecuteScalarAsync();
                logger?.LogDebug("ExecuteScalar completed: {Sql}", sql);
                
                return result is T value ? value : default(T);
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "ExecuteScalar failed: {Sql}", sql);
                throw;
            }
        }

        public async Task<int> ExecuteNonQueryAsync(string sql, params DuckDBParameter[] parameters)
        {
            try
            {
                await using var command = await CreateCommandAsync(sql);
                if (parameters.Length > 0)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                var result = await command.ExecuteNonQueryAsync();
                logger?.LogDebug("ExecuteNonQuery completed: {Sql}, Affected rows: {Rows}", sql, result);
                
                return result;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "ExecuteNonQuery failed: {Sql}", sql);
                throw;
            }
        }

        public async Task<DuckDBDataReader> ExecuteReaderAsync(string sql, params DuckDBParameter[] parameters)
        {
            try
            {
                var command = await CreateCommandAsync(sql);
                if (parameters.Length > 0)
                {
                    command.Parameters.AddRange(parameters);
                }

                var reader = (DuckDBDataReader)await command.ExecuteReaderAsync();
                logger?.LogDebug("ExecuteReader completed: {Sql}", sql);
                
                return reader;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "ExecuteReader failed: {Sql}", sql);
                throw;
            }
        }

        public async Task<IEnumerable<T>> QueryAsync<T>(string sql, Func<DbDataReader, T> mapper, params DuckDBParameter[] parameters)
        {
            var results = new List<T>();
            
            try
            {
                await using var reader = await ExecuteReaderAsync(sql, parameters);
                while (await reader.ReadAsync())
                {
                    results.Add(mapper(reader));
                }
                
                logger?.LogDebug("Query completed: {Sql}, Results: {Count}", sql, results.Count);
                return results;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Query failed: {Sql}", sql);
                throw;
            }
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("Transaction already in progress");
            }

            _transaction = (DuckDBTransaction)await Connection.BeginTransactionAsync();
            logger?.LogDebug("Transaction started");
        }

        public bool HasActiveTransaction => _transaction != null;

        public async Task CommitTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress");
            }
            
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
            logger?.LogDebug("Transaction committed");
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress");
            }
            
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
            logger?.LogDebug("Transaction rolled back");
        }

        public async Task<bool> TableExistsAsync(string tableName)
        {
            const string sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1";
            var parameter = new DuckDBParameter(tableName);
            var count = await ExecuteScalarAsync<long>(sql, parameter);
            return count > 0;
        }

        public async Task CreateTableAsync(string createTableSql)
        {
            await ExecuteNonQueryAsync(createTableSql);
            logger?.LogInformation("Table created successfully");
        }

        public void Dispose() {
            if (_disposed) return;
            _transaction?.Dispose();
            _connection?.Dispose();
            _disposed = true;
            logger?.LogDebug("DuckDB context disposed");
        }
    }
}
