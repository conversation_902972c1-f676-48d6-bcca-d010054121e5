using TimeLens.Client.Models;

namespace TimeLens.Client.Services
{
    /// <summary>
    /// NewsItemConceptTheme API服务接口，用于WebAssembly客户端
    /// </summary>
    public interface INewsItemConceptThemeApiService
    {
        // 基本CRUD操作
        Task<IEnumerable<NewsItemConceptTheme>> GetAllAsync();
        Task<NewsItemConceptTheme?> GetByIdAsync(string id);
        Task<NewsItemConceptTheme> CreateAsync(NewsItemConceptTheme newsItemConceptTheme);
        Task<NewsItemConceptTheme> UpdateAsync(string id, NewsItemConceptTheme newsItemConceptTheme);
        Task<bool> DeleteAsync(string id);

        // NewsItemConceptTheme特定的查询方法
        Task<IEnumerable<ConceptTheme>> GetConceptThemesByNewsItemIdAsync(string newsItemId);
        Task<IEnumerable<NewsItem>> GetNewsItemsByConceptThemeIdAsync(string conceptThemeId);
        Task<Dictionary<string, List<ConceptTheme>>> GetConceptThemesByNewsItemIdsAsync(IEnumerable<string> newsItemIds);
        Task<Dictionary<string, List<NewsItem>>> GetNewsItemsByConceptThemeIdsAsync(IEnumerable<string> conceptThemeIds);
        Task AddNewsItemConceptThemeAsync(string newsItemId, string conceptThemeId);
        Task AddNewsItemConceptThemesAsync(string newsItemId, IEnumerable<string> conceptThemeIds);
        Task RemoveNewsItemConceptThemeAsync(string newsItemId, string conceptThemeId);
        Task RemoveAllConceptThemesFromNewsItemAsync(string newsItemId);
        Task RemoveAllNewsItemsFromConceptThemeAsync(string conceptThemeId);
        Task<bool> IsNewsItemConceptThemeLinkedAsync(string newsItemId, string conceptThemeId);
        Task<int> GetNewsItemCountByConceptThemeIdAsync(string conceptThemeId);
        Task<int> GetConceptThemeCountByNewsItemIdAsync(string newsItemId);
        Task<IEnumerable<(ConceptTheme Theme, int NewsCount)>> GetMostPopularConceptThemesAsync(int topCount = 10);
        Task<Dictionary<string, int>> GetConceptThemeNewsCountStatisticsAsync();

        // 批量操作
        Task<IEnumerable<NewsItemConceptTheme>> CreateBatchAsync(IEnumerable<NewsItemConceptTheme> newsItemConceptThemes);
    }
}
