using System.Net.Http.Json;
using TimeLens.Client.Models;

namespace TimeLens.Client.Services
{
    /// <summary>
    /// LatestNewsSyncState HTTP API服务实现
    /// </summary>
    public class LatestNewsSyncStateApiService(HttpClient httpClient, ILogger<LatestNewsSyncStateApiService> logger) : ILatestNewsSyncStateApiService
    {
        private readonly string _baseUrl = "api/latestnewssyncstate";

        public async Task<IEnumerable<LatestNewsSyncState>> GetAllAsync()
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(_baseUrl);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving all latest news sync states");
                throw;
            }
        }

        public async Task<LatestNewsSyncState?> GetByIdAsync(string id)
        {
            try
            {
                return await httpClient.GetFromJsonAsync<LatestNewsSyncState>($"{_baseUrl}/{id}");
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("404"))
            {
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync state with ID {Id}", id);
                throw;
            }
        }

        public async Task<LatestNewsSyncState> CreateAsync(LatestNewsSyncState latestNewsSyncState)
        {
            try
            {
                var response = await httpClient.PostAsJsonAsync(_baseUrl, latestNewsSyncState);
                response.EnsureSuccessStatusCode();
                
                var created = await response.Content.ReadFromJsonAsync<LatestNewsSyncState>();
                return created ?? throw new InvalidOperationException("Failed to deserialize created latest news sync state");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating latest news sync state");
                throw;
            }
        }

        public async Task<LatestNewsSyncState> UpdateAsync(string id, LatestNewsSyncState latestNewsSyncState)
        {
            try
            {
                var response = await httpClient.PutAsJsonAsync($"{_baseUrl}/{id}", latestNewsSyncState);
                response.EnsureSuccessStatusCode();
                
                var updated = await response.Content.ReadFromJsonAsync<LatestNewsSyncState>();
                return updated ?? throw new InvalidOperationException("Failed to deserialize updated latest news sync state");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating latest news sync state with ID {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                var response = await httpClient.DeleteAsync($"{_baseUrl}/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting latest news sync state with ID {Id}", id);
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetBySiteAsync(SiteCategory site)
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>($"{_baseUrl}/site/{site}");
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync states for site {Site}", site);
                throw;
            }
        }

        public async Task<LatestNewsSyncState?> GetBySyncIdAsync(string syncId)
        {
            try
            {
                return await httpClient.GetFromJsonAsync<LatestNewsSyncState>($"{_baseUrl}/syncid/{Uri.EscapeDataString(syncId)}");
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("404"))
            {
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync state with sync ID {SyncId}", syncId);
                throw;
            }
        }

        public async Task<LatestNewsSyncState?> GetBySiteAndSyncIdAsync(SiteCategory site, string syncId)
        {
            try
            {
                var url = $"{_baseUrl}/site/{site}/syncid/{Uri.EscapeDataString(syncId)}";
                return await httpClient.GetFromJsonAsync<LatestNewsSyncState>(url);
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("404"))
            {
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync state for site {Site} and sync ID {SyncId}", site, syncId);
                throw;
            }
        }

        public async Task<LatestNewsSyncState?> GetLatestBySiteAsync(SiteCategory site)
        {
            try
            {
                return await httpClient.GetFromJsonAsync<LatestNewsSyncState>($"{_baseUrl}/site/{site}/latest");
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("404"))
            {
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest sync state for site {Site}", site);
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetLatestForAllSitesAsync()
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>($"{_baseUrl}/latest/all");
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest sync states for all sites");
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var url = $"{_baseUrl}/daterange?startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync states by date range {StartDate} to {EndDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetByCreatedDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var url = $"{_baseUrl}/created/daterange?startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync states by created date range {StartDate} to {EndDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetByUpdatedDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var url = $"{_baseUrl}/updated/daterange?startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync states by updated date range {StartDate} to {EndDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetCreatedAfterAsync(DateTime date)
        {
            try
            {
                var url = $"{_baseUrl}/created/after?date={date:yyyy-MM-dd}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync states created after {Date}", date);
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetUpdatedAfterAsync(DateTime date)
        {
            try
            {
                var url = $"{_baseUrl}/updated/after?date={date:yyyy-MM-dd}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync states updated after {Date}", date);
                throw;
            }
        }

        public async Task<int> GetCountBySiteAsync(SiteCategory site)
        {
            try
            {
                return await httpClient.GetFromJsonAsync<int>($"{_baseUrl}/count/site/{site}");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving count for site {Site}", site);
                throw;
            }
        }

        public async Task<Dictionary<SiteCategory, int>> GetSiteStatisticsAsync()
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<Dictionary<SiteCategory, int>>($"{_baseUrl}/statistics/sites");
                return response ?? new Dictionary<SiteCategory, int>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving site statistics");
                throw;
            }
        }

        public async Task<int> GetCountByDateAsync(DateTime date)
        {
            try
            {
                var url = $"{_baseUrl}/count/date?date={date:yyyy-MM-dd}";
                return await httpClient.GetFromJsonAsync<int>(url);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving count for date {Date}", date);
                throw;
            }
        }

        public async Task<int> GetTotalCountAsync()
        {
            try
            {
                return await httpClient.GetFromJsonAsync<int>($"{_baseUrl}/count/total");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving total count");
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetRecentSyncStatesAsync(int count = 10)
        {
            try
            {
                var url = $"{_baseUrl}/recent?count={count}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving recent sync states");
                throw;
            }
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetRecentBySiteAsync(SiteCategory site, int count = 10)
        {
            try
            {
                var url = $"{_baseUrl}/recent/site/{site}?count={count}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<LatestNewsSyncState>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving recent sync states for site {Site}", site);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(SiteCategory site, string syncId)
        {
            try
            {
                var url = $"{_baseUrl}/exists/site/{site}/syncid/{Uri.EscapeDataString(syncId)}";
                return await httpClient.GetFromJsonAsync<bool>(url);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error checking existence for site {Site} and sync ID {SyncId}", site, syncId);
                throw;
            }
        }

        public async Task<LatestNewsSyncState> UpsertAsync(SiteCategory site, string syncId)
        {
            try
            {
                var url = $"{_baseUrl}/upsert";
                var request = new { Site = site, SyncId = syncId };
                var response = await httpClient.PostAsJsonAsync(url, request);
                response.EnsureSuccessStatusCode();
                
                var result = await response.Content.ReadFromJsonAsync<LatestNewsSyncState>();
                return result ?? throw new InvalidOperationException("Failed to deserialize upserted latest news sync state");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error upserting latest news sync state for site {Site} and sync ID {SyncId}", site, syncId);
                throw;
            }
        }

        public async Task<int> DeleteBySiteAsync(SiteCategory site)
        {
            try
            {
                var response = await httpClient.DeleteAsync($"{_baseUrl}/site/{site}");
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<int>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting sync states for site {Site}", site);
                throw;
            }
        }

        public async Task<int> DeleteOlderThanAsync(DateTime date)
        {
            try
            {
                var url = $"{_baseUrl}/cleanup/older?date={date:yyyy-MM-dd}";
                var response = await httpClient.DeleteAsync(url);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<int>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting sync states older than {Date}", date);
                throw;
            }
        }

        public async Task<int> CleanupDuplicatesAsync()
        {
            try
            {
                var response = await httpClient.DeleteAsync($"{_baseUrl}/cleanup/duplicates");
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<int>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error cleaning up duplicate sync states");
                throw;
            }
        }
    }
}
