using TimeLens.Client.Models;
using System.Collections.Concurrent;
using TimeLens.Client.Data.Repositories;

namespace TimeLens.Client.Services
{
using Nito.AsyncEx;

public class NewsService {
        private readonly ILatestNewsSyncStateRepository? _latestNewsSyncStateRepository;
        private readonly IConceptThemeRepository? _conceptThemeRepository;
        private readonly INewsItemRepository? _newsItemRepository;
        private readonly INewsItemConceptThemeRepository? _newsItemConceptThemeRepository;

        private readonly ILatestNewsSyncStateApiService? _latestNewsSyncStateApiService;
        private readonly IConceptThemeApiService? _conceptThemeApiService;
        private readonly INewsItemApiService? _newsItemApiService;
        private readonly INewsItemConceptThemeApiService? _newsItemConceptThemeApiService;

        private readonly bool _isWebAssembly;
        private readonly AsyncLock _lockObject = new();

        // Constructor for server environment (with repositories)
        public NewsService(
            ILatestNewsSyncStateRepository latestNewsSyncStateRepository,
            IConceptThemeRepository conceptThemeRepository,
            INewsItemRepository newsItemRepository,
            INewsItemConceptThemeRepository newsItemConceptThemeRepository)
        {
            _latestNewsSyncStateRepository = latestNewsSyncStateRepository;
            _conceptThemeRepository = conceptThemeRepository;
            _newsItemRepository = newsItemRepository;
            _newsItemConceptThemeRepository = newsItemConceptThemeRepository;
            _isWebAssembly = false;
        }

        // Constructor for WebAssembly environment (with API services)
        public NewsService(
            ILatestNewsSyncStateApiService latestNewsSyncStateApiService,
            IConceptThemeApiService conceptThemeApiService,
            INewsItemApiService newsItemApiService,
            INewsItemConceptThemeApiService newsItemConceptThemeApiService)
        {
            _latestNewsSyncStateApiService = latestNewsSyncStateApiService;
            _conceptThemeApiService = conceptThemeApiService;
            _newsItemApiService = newsItemApiService;
            _newsItemConceptThemeApiService = newsItemConceptThemeApiService;
            _isWebAssembly = true;
        }

        // 新浪财经全量数据：https://zhibo.sina.com.cn/api/zhibo/feed?page=1&page_size=20&zhibo_id=152&tag_id=0&dire=f&dpc=1&pagesize=20&_=1751557422167
        // 新浪财经增量数据：https://zhibo.sina.com.cn/api/zhibo/feed?page=1&page_size=20&zhibo_id=152&tag_id=0&dire=f&dpc=1&pagesize=20&id=「最新的 id」&type=0/1&_=1751557422167
        // 东方财富：https://np-weblist.eastmoney.com/comm/web/getFastNewsList?client=web&biz=web_724&fastColumn=102&sortEnd=&pageSize=50&req_trace=1751557872993&_=1751557872994
        // 同花顺：https://news.10jqka.com.cn/tapp/news/push/stock/?page=1&tag=&track=website&pagesize=400
        
        /// <summary>
        /// 更新新闻数据（由后台任务调用）
        /// </summary>
        public async Task AppendNewsItemsAsync(NewsItem newItem, string maxSyncId, SiteCategory siteCategory, List<ConceptTheme> conceptThemes)
        {
            using (await _lockObject.LockAsync())
            {
                if (_isWebAssembly)
                {
                    // WebAssembly environment - use API services
                    // Note: This method is typically called from background tasks which run on the server,
                    // so this WebAssembly path might not be used in practice
                    throw new NotSupportedException("AppendNewsItemsAsync is not supported in WebAssembly environment. This method should only be called from server-side background tasks.");
                }
                else
                {
                    // Server environment - use repositories
                    // 查看今天有没有相关的概念主题，插入概念主题
                    var existingConceptThemes = await _conceptThemeRepository!.GetThemesCreatedAfterAsync(DateTime.Today);
                    var conceptThemesToInsert = conceptThemes
                        .Where(ct => existingConceptThemes.All(exist => exist.Name != ct.Name))
                        .ToList();
                    var insertedConceptThemes = await _conceptThemeRepository.AddRangeAsync(conceptThemesToInsert);
                    var conceptThemeIds = insertedConceptThemes.Select(ct => ct.Id).ToList();
                    // 查看今天有没有相关的新闻
                    var existNewsItems = await _newsItemRepository!.SearchByTitleAsync(newItem.Title);
                    var insertedNew = existNewsItems.FirstOrDefault() ?? await _newsItemRepository.AddAsync(newItem);
                    // 插入关联表
                    if (conceptThemeIds.Count > 0)
                    {
                        foreach (var conceptThemeId in conceptThemeIds)
                        {
                            var isExist = await _newsItemConceptThemeRepository!.IsNewsItemConceptThemeLinkedAsync(insertedNew.Id, conceptThemeId);
                            if (isExist) continue;
                            await _newsItemConceptThemeRepository.AddNewsItemConceptThemeAsync(insertedNew.Id, conceptThemeId);
                        }
                    }
                    // 更新最新记录
                    await _latestNewsSyncStateRepository!.UpsertAsync(siteCategory, maxSyncId);
                }
            }
        }

        public async Task<List<NewsItem>> GetNewsItemsAsync(string? searchTerm, string? company, NewsCategory? category = null)
        {
            List<NewsItem> query = [];

            if (_isWebAssembly)
            {
                // WebAssembly environment - use API services
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = (await _newsItemApiService!.SearchByTitleAsync(searchTerm)).ToList();
                }
                else
                {
                    query = (await _newsItemApiService!.GetAllAsync()).ToList();
                }

                if (!string.IsNullOrEmpty(company))
                {
                    query = query.Where(n => n.Company == company).ToList();
                }

                if (category.HasValue)
                {
                    query = query.Where(n => n.Category == category.Value).ToList();
                }
            }
            else
            {
                // Server environment - use repositories
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    //(await _newsItemRepository!.GetPagedAsync(n => n.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase), n => n.PublishDate, ascending: false)).ToList();
                    query = (await _newsItemRepository!.SearchByTitleAsync(searchTerm)).ToList();
                }
                else
                {
                    //(await _newsItemRepository!.GetPagedAsync(_ => true, n => n.PublishDate, ascending: false)).ToList();
                    query = (await _newsItemRepository!.GetAllAsync()).ToList(); 
                }

                if (!string.IsNullOrEmpty(company))
                {
                    query = query.Where(n => n.Company == company).ToList();
                }

                if (category.HasValue)
                {
                    query = query.Where(n => n.Category == category.Value).ToList();
                }
            }

            return query;
        }

        public async Task<NewsItem?> GetNewsItemByIdAsync(string? id)
        {
            if (string.IsNullOrEmpty(id)) return null;

            if (_isWebAssembly)
            {
                return await _newsItemApiService!.GetByIdAsync(id);
            }
            else
            {
                return await _newsItemRepository!.GetByIdAsync(id);
            }
        }

        public async Task<List<NewsItem>> GetRelatedNewsAsync(string newsId)
        {
            if (_isWebAssembly)
            {
                // For WebAssembly, we'll need to implement this differently
                // For now, return empty list as this might require a specific API endpoint
                return [];
            }
            else
            {
                var query = await _newsItemRepository!.GetRelatedNewsAsync(newsId);
                return query.Take(5).ToList();
            }
        }
    }
}
