using TimeLens.Client.Models;

namespace TimeLens.Client.Services
{
    /// <summary>
    /// LatestNewsSyncState API服务接口，用于WebAssembly客户端
    /// </summary>
    public interface ILatestNewsSyncStateApiService
    {
        // 基本CRUD操作
        Task<IEnumerable<LatestNewsSyncState>> GetAllAsync();
        Task<LatestNewsSyncState?> GetByIdAsync(string id);
        Task<LatestNewsSyncState> CreateAsync(LatestNewsSyncState latestNewsSyncState);
        Task<LatestNewsSyncState> UpdateAsync(string id, LatestNewsSyncState latestNewsSyncState);
        Task<bool> DeleteAsync(string id);

        // LatestNewsSyncState特定的查询方法
        Task<IEnumerable<LatestNewsSyncState>> GetBySiteAsync(SiteCategory site);
        Task<LatestNewsSyncState?> GetBySyncIdAsync(string syncId);
        Task<LatestNewsSyncState?> GetBySiteAndSyncIdAsync(SiteCategory site, string syncId);
        Task<LatestNewsSyncState?> GetLatestBySiteAsync(SiteCategory site);
        Task<IEnumerable<LatestNewsSyncState>> GetLatestForAllSitesAsync();
        Task<IEnumerable<LatestNewsSyncState>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<LatestNewsSyncState>> GetByCreatedDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<LatestNewsSyncState>> GetByUpdatedDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<LatestNewsSyncState>> GetCreatedAfterAsync(DateTime date);
        Task<IEnumerable<LatestNewsSyncState>> GetUpdatedAfterAsync(DateTime date);
        
        // 统计方法
        Task<int> GetCountBySiteAsync(SiteCategory site);
        Task<Dictionary<SiteCategory, int>> GetSiteStatisticsAsync();
        Task<int> GetCountByDateAsync(DateTime date);
        Task<int> GetTotalCountAsync();

        // 高级查询
        Task<IEnumerable<LatestNewsSyncState>> GetRecentSyncStatesAsync(int count = 10);
        Task<IEnumerable<LatestNewsSyncState>> GetRecentBySiteAsync(SiteCategory site, int count = 10);
        Task<bool> ExistsAsync(SiteCategory site, string syncId);
        Task<LatestNewsSyncState> UpsertAsync(SiteCategory site, string syncId);
        Task<int> DeleteBySiteAsync(SiteCategory site);
        Task<int> DeleteOlderThanAsync(DateTime date);
        Task<int> CleanupDuplicatesAsync();
    }
}
