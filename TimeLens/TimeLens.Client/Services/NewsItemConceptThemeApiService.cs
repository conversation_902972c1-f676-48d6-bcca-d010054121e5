using System.Net.Http.Json;
using TimeLens.Client.Models;

namespace TimeLens.Client.Services
{
    /// <summary>
    /// NewsItemConceptTheme HTTP API服务实现
    /// </summary>
    public class NewsItemConceptThemeApiService(HttpClient httpClient, ILogger<NewsItemConceptThemeApiService> logger) : INewsItemConceptThemeApiService
    {
        private readonly string _baseUrl = "api/newsitemconcepttheme";

        public async Task<IEnumerable<NewsItemConceptTheme>> GetAllAsync()
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<IEnumerable<NewsItemConceptTheme>>(_baseUrl);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving all news item concept themes");
                throw;
            }
        }

        public async Task<NewsItemConceptTheme?> GetByIdAsync(string id)
        {
            try
            {
                return await httpClient.GetFromJsonAsync<NewsItemConceptTheme>($"{_baseUrl}/{id}");
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("404"))
            {
                return null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news item concept theme with ID {Id}", id);
                throw;
            }
        }

        public async Task<NewsItemConceptTheme> CreateAsync(NewsItemConceptTheme newsItemConceptTheme)
        {
            try
            {
                var response = await httpClient.PostAsJsonAsync(_baseUrl, newsItemConceptTheme);
                response.EnsureSuccessStatusCode();
                
                var created = await response.Content.ReadFromJsonAsync<NewsItemConceptTheme>();
                return created ?? throw new InvalidOperationException("Failed to deserialize created news item concept theme");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating news item concept theme");
                throw;
            }
        }

        public async Task<NewsItemConceptTheme> UpdateAsync(string id, NewsItemConceptTheme newsItemConceptTheme)
        {
            try
            {
                var response = await httpClient.PutAsJsonAsync($"{_baseUrl}/{id}", newsItemConceptTheme);
                response.EnsureSuccessStatusCode();
                
                var updated = await response.Content.ReadFromJsonAsync<NewsItemConceptTheme>();
                return updated ?? throw new InvalidOperationException("Failed to deserialize updated news item concept theme");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating news item concept theme with ID {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                var response = await httpClient.DeleteAsync($"{_baseUrl}/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting news item concept theme with ID {Id}", id);
                throw;
            }
        }

        public async Task<Dictionary<string, List<ConceptTheme>>> GetConceptThemesByNewsItemIdsAsync(IEnumerable<string> newsItemIds)
        {
            try
            {
                var response = await httpClient.PostAsJsonAsync($"{_baseUrl}/newsitems/batch", newsItemIds);
                response.EnsureSuccessStatusCode();

                var result = await response.Content.ReadFromJsonAsync<Dictionary<string, List<ConceptTheme>>>();
                return result ?? new Dictionary<string, List<ConceptTheme>>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept themes for multiple news items");
                throw;
            }
        }

        public async Task<Dictionary<string, List<NewsItem>>> GetNewsItemsByConceptThemeIdsAsync(IEnumerable<string> conceptThemeIds)
        {
            try
            {
                var response = await httpClient.PostAsJsonAsync($"{_baseUrl}/conceptthemes/batch", conceptThemeIds);
                response.EnsureSuccessStatusCode();

                var result = await response.Content.ReadFromJsonAsync<Dictionary<string, List<NewsItem>>>();
                return result ?? new Dictionary<string, List<NewsItem>>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items for multiple concept themes");
                throw;
            }
        }

        public async Task<bool> IsNewsItemConceptThemeLinkedAsync(string newsItemId, string conceptThemeId)
        {
            try
            {
                var url = $"{_baseUrl}/linked/newsitem/{newsItemId}/concepttheme/{conceptThemeId}";
                return await httpClient.GetFromJsonAsync<bool>(url);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error checking if news item {NewsItemId} is linked to concept theme {ConceptThemeId}", newsItemId, conceptThemeId);
                throw;
            }
        }

        public async Task AddNewsItemConceptThemeAsync(string newsItemId, string conceptThemeId)
        {
            try
            {
                var request = new { NewsItemId = newsItemId, ConceptThemeId = conceptThemeId };
                var response = await httpClient.PostAsJsonAsync($"{_baseUrl}/link", request);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error linking news item {NewsItemId} to concept theme {ConceptThemeId}", newsItemId, conceptThemeId);
                throw;
            }
        }

        public async Task AddNewsItemConceptThemesAsync(string newsItemId, IEnumerable<string> conceptThemeIds)
        {
            try
            {
                var request = new { NewsItemId = newsItemId, ConceptThemeIds = conceptThemeIds };
                var response = await httpClient.PostAsJsonAsync($"{_baseUrl}/link/batch", request);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error linking news item {NewsItemId} to multiple concept themes", newsItemId);
                throw;
            }
        }

        public async Task RemoveNewsItemConceptThemeAsync(string newsItemId, string conceptThemeId)
        {
            try
            {
                var url = $"{_baseUrl}/unlink/newsitem/{newsItemId}/concepttheme/{conceptThemeId}";
                var response = await httpClient.DeleteAsync(url);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error unlinking news item {NewsItemId} from concept theme {ConceptThemeId}", newsItemId, conceptThemeId);
                throw;
            }
        }

        public async Task RemoveAllConceptThemesFromNewsItemAsync(string newsItemId)
        {
            try
            {
                var response = await httpClient.DeleteAsync($"{_baseUrl}/newsitem/{newsItemId}");
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error removing all concept themes from news item {NewsItemId}", newsItemId);
                throw;
            }
        }

        public async Task RemoveAllNewsItemsFromConceptThemeAsync(string conceptThemeId)
        {
            try
            {
                var response = await httpClient.DeleteAsync($"{_baseUrl}/concepttheme/{conceptThemeId}");
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error removing all news items from concept theme {ConceptThemeId}", conceptThemeId);
                throw;
            }
        }

        public async Task<IEnumerable<ConceptTheme>> GetConceptThemesByNewsItemIdAsync(string newsItemId)
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<IEnumerable<ConceptTheme>>($"{_baseUrl}/newsitem/{newsItemId}/conceptthemes");
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept themes for news item {NewsItemId}", newsItemId);
                throw;
            }
        }

        public async Task<IEnumerable<NewsItem>> GetNewsItemsByConceptThemeIdAsync(string conceptThemeId)
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<IEnumerable<NewsItem>>($"{_baseUrl}/concepttheme/{conceptThemeId}/newsitems");
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items for concept theme {ConceptThemeId}", conceptThemeId);
                throw;
            }
        }

        public async Task<int> GetNewsItemCountByConceptThemeIdAsync(string conceptThemeId)
        {
            try
            {
                return await httpClient.GetFromJsonAsync<int>($"{_baseUrl}/count/concepttheme/{conceptThemeId}");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news item count for concept theme {ConceptThemeId}", conceptThemeId);
                throw;
            }
        }

        public async Task<int> GetConceptThemeCountByNewsItemIdAsync(string newsItemId)
        {
            try
            {
                return await httpClient.GetFromJsonAsync<int>($"{_baseUrl}/count/newsitem/{newsItemId}");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept theme count for news item {NewsItemId}", newsItemId);
                throw;
            }
        }

        public async Task<IEnumerable<(ConceptTheme Theme, int NewsCount)>> GetMostPopularConceptThemesAsync(int topCount = 10)
        {
            try
            {
                var url = $"{_baseUrl}/popular?topCount={topCount}";
                var response = await httpClient.GetFromJsonAsync<IEnumerable<(ConceptTheme Theme, int NewsCount)>>(url);
                return response ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving most popular concept themes");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetConceptThemeNewsCountStatisticsAsync()
        {
            try
            {
                var response = await httpClient.GetFromJsonAsync<Dictionary<string, int>>($"{_baseUrl}/statistics/concepttheme-counts");
                return response ?? new Dictionary<string, int>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept theme news count statistics");
                throw;
            }
        }

        public async Task<IEnumerable<NewsItemConceptTheme>> CreateBatchAsync(IEnumerable<NewsItemConceptTheme> newsItemConceptThemes)
        {
            try
            {
                var response = await httpClient.PostAsJsonAsync($"{_baseUrl}/batch", newsItemConceptThemes);
                response.EnsureSuccessStatusCode();

                var result = await response.Content.ReadFromJsonAsync<IEnumerable<NewsItemConceptTheme>>();
                return result ?? [];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating batch news item concept themes");
                throw;
            }
        }
    }
}
