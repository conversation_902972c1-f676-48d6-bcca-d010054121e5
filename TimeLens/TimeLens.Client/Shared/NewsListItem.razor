@using TimeLens.Client.Models

<div class="news-item" @onclick="() => OnItemClick.InvokeAsync(NewsItem)">
    <div class="news-header">
        <h3 class="news-title">@NewsItem.Title</h3>
        <div class="news-meta">
            <span class="news-date">@NewsItem.PublishDate.ToString("yyyy-MM-dd HH:mm")</span>
            <span class="news-company">@NewsItem.Company</span>
        </div>
    </div>
    
    <div class="news-tags">
        @if (@NewsItem.Category != NewsCategory.其他)
        {
            <Tag Color="TagColor.Blue">@NewsItem.Category.ToString()</Tag>
        }
        @foreach (var tag in NewsItem.Tags.Take(2))
        {
            <Tag Color="TagColor.Green">@tag</Tag>
        }
    </div>
    
    @* @if (!string.IsNullOrEmpty(NewsItem.Summary)) *@
    @* { *@
    @*     <div class="news-summary">@NewsItem.Summary</div> *@
    @* } *@
</div>

<style>
.news-item {
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 6px;
    background: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s;
}

.news-item:hover {
    background: #f8f9fa;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.news-header {
    margin-bottom: 8px;
}

.news-title {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 4px 0;
    color: #333;
}

.news-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #666;
}

.news-tags {
    display: flex;
    gap: 8px;
    margin: 8px 0;
    flex-wrap: wrap;
}

.news-summary {
    font-size: 14px;
    color: #444;
    line-height: 1.5;
}
.news-company {
    font-weight: 600;
    color: #37352f;
    font-size: 16px;
}
</style>

@code {
    [Parameter]
    public required NewsItem NewsItem { get; set; }

    [Parameter]
    public EventCallback<NewsItem> OnItemClick { get; set; }
}
