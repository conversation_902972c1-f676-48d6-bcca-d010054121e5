using Microsoft.AspNetCore.Mvc;
using TimeLens.Client.Data.Repositories;
using TimeLens.Client.Models;
using System.ComponentModel.DataAnnotations;

namespace TimeLens.Controllers
{
    /// <summary>
    /// LatestNewsSyncState API控制器，提供RESTful API接口
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class LatestNewsSyncStateController(
        ILatestNewsSyncStateRepository latestNewsSyncStateRepository,
        ILogger<LatestNewsSyncStateController> logger)
        : ControllerBase
    {
        /// <summary>
        /// 获取所有同步状态
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetAllAsync()
        {
            try
            {
                var syncStates = await latestNewsSyncStateRepository.GetAllAsync();
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving all latest news sync states");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据ID获取同步状态
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<LatestNewsSyncState>> GetByIdAsync(string id)
        {
            try
            {
                var syncState = await latestNewsSyncStateRepository.GetByIdAsync(id);
                if (syncState == null)
                {
                    return NotFound($"LatestNewsSyncState with ID {id} not found");
                }
                return Ok(syncState);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest news sync state {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 创建新的同步状态
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<LatestNewsSyncState>> CreateAsync([FromBody] LatestNewsSyncState latestNewsSyncState)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var created = await latestNewsSyncStateRepository.AddAsync(latestNewsSyncState);
                return CreatedAtAction(nameof(GetByIdAsync), new { id = created.Id }, created);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating latest news sync state");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 更新同步状态
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<LatestNewsSyncState>> UpdateAsync(string id, [FromBody] LatestNewsSyncState latestNewsSyncState)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != latestNewsSyncState.Id)
                {
                    return BadRequest("ID mismatch");
                }

                var updated = await latestNewsSyncStateRepository.UpdateAsync(latestNewsSyncState);
                return Ok(updated);
            }
            catch (InvalidOperationException)
            {
                return NotFound($"LatestNewsSyncState with ID {id} not found");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating latest news sync state {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除同步状态
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> DeleteAsync(string id)
        {
            try
            {
                var result = await latestNewsSyncStateRepository.DeleteAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting latest news sync state {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据站点获取同步状态
        /// </summary>
        [HttpGet("site/{site}")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetBySiteAsync(SiteCategory site)
        {
            try
            {
                var syncStates = await latestNewsSyncStateRepository.GetBySiteAsync(site);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync states for site {Site}", site);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据同步ID获取同步状态
        /// </summary>
        [HttpGet("syncid/{syncId}")]
        public async Task<ActionResult<LatestNewsSyncState>> GetBySyncIdAsync(string syncId)
        {
            try
            {
                var syncState = await latestNewsSyncStateRepository.GetBySyncIdAsync(syncId);
                if (syncState == null)
                {
                    return NotFound($"LatestNewsSyncState with sync ID {syncId} not found");
                }
                return Ok(syncState);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync state with sync ID {SyncId}", syncId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据站点和同步ID获取同步状态
        /// </summary>
        [HttpGet("site/{site}/syncid/{syncId}")]
        public async Task<ActionResult<LatestNewsSyncState>> GetBySiteAndSyncIdAsync(SiteCategory site, string syncId)
        {
            try
            {
                var syncState = await latestNewsSyncStateRepository.GetBySiteAndSyncIdAsync(site, syncId);
                if (syncState == null)
                {
                    return NotFound($"LatestNewsSyncState for site {site} and sync ID {syncId} not found");
                }
                return Ok(syncState);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync state for site {Site} and sync ID {SyncId}", site, syncId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取指定站点的最新同步状态
        /// </summary>
        [HttpGet("site/{site}/latest")]
        public async Task<ActionResult<LatestNewsSyncState>> GetLatestBySiteAsync(SiteCategory site)
        {
            try
            {
                var syncState = await latestNewsSyncStateRepository.GetLatestBySiteAsync(site);
                if (syncState == null)
                {
                    return NotFound($"No sync state found for site {site}");
                }
                return Ok(syncState);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest sync state for site {Site}", site);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取所有站点的最新同步状态
        /// </summary>
        [HttpGet("latest/all")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetLatestForAllSitesAsync()
        {
            try
            {
                var syncStates = await latestNewsSyncStateRepository.GetLatestForAllSitesAsync();
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving latest sync states for all sites");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据日期范围获取同步状态
        /// </summary>
        [HttpGet("daterange")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetByDateRangeAsync(
            [FromQuery, Required] DateTime startDate,
            [FromQuery, Required] DateTime endDate)
        {
            try
            {
                if (startDate > endDate)
                {
                    return BadRequest("Start date must be before end date");
                }

                var syncStates = await latestNewsSyncStateRepository.GetByDateRangeAsync(startDate, endDate);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync states for date range {StartDate} to {EndDate}", startDate, endDate);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据创建日期范围获取同步状态
        /// </summary>
        [HttpGet("created/daterange")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetByCreatedDateRangeAsync(
            [FromQuery, Required] DateTime startDate,
            [FromQuery, Required] DateTime endDate)
        {
            try
            {
                if (startDate > endDate)
                {
                    return BadRequest("Start date must be before end date");
                }

                var syncStates = await latestNewsSyncStateRepository.GetByCreatedDateRangeAsync(startDate, endDate);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync states by created date range {StartDate} to {EndDate}", startDate, endDate);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据更新日期范围获取同步状态
        /// </summary>
        [HttpGet("updated/daterange")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetByUpdatedDateRangeAsync(
            [FromQuery, Required] DateTime startDate,
            [FromQuery, Required] DateTime endDate)
        {
            try
            {
                if (startDate > endDate)
                {
                    return BadRequest("Start date must be before end date");
                }

                var syncStates = await latestNewsSyncStateRepository.GetByUpdatedDateRangeAsync(startDate, endDate);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync states by updated date range {StartDate} to {EndDate}", startDate, endDate);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取指定日期之后创建的同步状态
        /// </summary>
        [HttpGet("created/after")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetCreatedAfterAsync([FromQuery, Required] DateTime date)
        {
            try
            {
                var syncStates = await latestNewsSyncStateRepository.GetCreatedAfterAsync(date);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync states created after {Date}", date);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取指定日期之后更新的同步状态
        /// </summary>
        [HttpGet("updated/after")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetUpdatedAfterAsync([FromQuery, Required] DateTime date)
        {
            try
            {
                var syncStates = await latestNewsSyncStateRepository.GetUpdatedAfterAsync(date);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving sync states updated after {Date}", date);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取指定站点的同步状态数量
        /// </summary>
        [HttpGet("count/site/{site}")]
        public async Task<ActionResult<int>> GetCountBySiteAsync(SiteCategory site)
        {
            try
            {
                var count = await latestNewsSyncStateRepository.GetCountBySiteAsync(site);
                return Ok(count);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving count for site {Site}", site);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取站点统计信息
        /// </summary>
        [HttpGet("statistics/sites")]
        public async Task<ActionResult<Dictionary<SiteCategory, int>>> GetSiteStatisticsAsync()
        {
            try
            {
                var statistics = await latestNewsSyncStateRepository.GetSiteStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving site statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取指定日期的同步状态数量
        /// </summary>
        [HttpGet("count/date")]
        public async Task<ActionResult<int>> GetCountByDateAsync([FromQuery, Required] DateTime date)
        {
            try
            {
                var count = await latestNewsSyncStateRepository.GetCountByDateAsync(date);
                return Ok(count);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving count for date {Date}", date);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取总数量
        /// </summary>
        [HttpGet("count/total")]
        public async Task<ActionResult<int>> GetTotalCountAsync()
        {
            try
            {
                var count = await latestNewsSyncStateRepository.GetTotalCountAsync();
                return Ok(count);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving total count");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取最近的同步状态
        /// </summary>
        [HttpGet("recent")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetRecentSyncStatesAsync([FromQuery] int count = 10)
        {
            try
            {
                if (count <= 0 || count > 100)
                {
                    return BadRequest("Count must be between 1 and 100");
                }

                var syncStates = await latestNewsSyncStateRepository.GetRecentSyncStatesAsync(count);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving recent sync states");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取指定站点的最近同步状态
        /// </summary>
        [HttpGet("recent/site/{site}")]
        public async Task<ActionResult<IEnumerable<LatestNewsSyncState>>> GetRecentBySiteAsync(SiteCategory site, [FromQuery] int count = 10)
        {
            try
            {
                if (count <= 0 || count > 100)
                {
                    return BadRequest("Count must be between 1 and 100");
                }

                var syncStates = await latestNewsSyncStateRepository.GetRecentBySiteAsync(site, count);
                return Ok(syncStates);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving recent sync states for site {Site}", site);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 检查同步状态是否存在
        /// </summary>
        [HttpGet("exists/site/{site}/syncid/{syncId}")]
        public async Task<ActionResult<bool>> ExistsAsync(SiteCategory site, string syncId)
        {
            try
            {
                var exists = await latestNewsSyncStateRepository.ExistsAsync(site, syncId);
                return Ok(exists);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error checking existence for site {Site} and sync ID {SyncId}", site, syncId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 插入或更新同步状态
        /// </summary>
        [HttpPost("upsert")]
        public async Task<ActionResult<LatestNewsSyncState>> UpsertAsync([FromBody] UpsertRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await latestNewsSyncStateRepository.UpsertAsync(request.Site, request.SyncId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error upserting sync state for site {Site} and sync ID {SyncId}", request.Site, request.SyncId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除指定站点的所有同步状态
        /// </summary>
        [HttpDelete("site/{site}")]
        public async Task<ActionResult<int>> DeleteBySiteAsync(SiteCategory site)
        {
            try
            {
                var deletedCount = await latestNewsSyncStateRepository.DeleteBySiteAsync(site);
                return Ok(deletedCount);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting sync states for site {Site}", site);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除指定日期之前的同步状态
        /// </summary>
        [HttpDelete("cleanup/older")]
        public async Task<ActionResult<int>> DeleteOlderThanAsync([FromQuery, Required] DateTime date)
        {
            try
            {
                var deletedCount = await latestNewsSyncStateRepository.DeleteOlderThanAsync(date);
                return Ok(deletedCount);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting sync states older than {Date}", date);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 清理重复的同步状态
        /// </summary>
        [HttpDelete("cleanup/duplicates")]
        public async Task<ActionResult<int>> CleanupDuplicatesAsync()
        {
            try
            {
                var deletedCount = await latestNewsSyncStateRepository.CleanupDuplicatesAsync();
                return Ok(deletedCount);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error cleaning up duplicate sync states");
                return StatusCode(500, "Internal server error");
            }
        }
    }

    /// <summary>
    /// Upsert请求模型
    /// </summary>
    public class UpsertRequest
    {
        public required SiteCategory Site { get; set; }
        public required string SyncId { get; set; }
    }
}
