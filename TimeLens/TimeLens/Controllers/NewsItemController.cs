using Microsoft.AspNetCore.Mvc;
using TimeLens.Client.Data.Repositories;
using TimeLens.Client.Models;
using System.ComponentModel.DataAnnotations;

namespace TimeLens.Controllers
{
    /// <summary>
    /// NewsItem API控制器，提供RESTful API接口
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class NewsItemController(INewsItemRepository newsItemRepository, ILogger<NewsItemController> logger)
        : ControllerBase
    {
        /// <summary>
        /// 获取所有新闻项目
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<NewsItem>>> GetAllAsync()
        {
            try
            {
                var newsItems = await newsItemRepository.GetAllAsync();
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving all news items");
                return StatusCode(500, "Internal server error");
            }
        }
        
        /// <summary>
        /// 根据分页获取新闻项目
        /// </summary>
        

        /// <summary>
        /// 根据ID获取新闻项目
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<NewsItem>> GetByIdAsync(string id)
        {
            try
            {
                var newsItem = await newsItemRepository.GetByIdAsync(id);
                if (newsItem == null)
                {
                    return NotFound($"NewsItem with ID {id} not found");
                }
                return Ok(newsItem);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news item {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 创建新的新闻项目
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<NewsItem>> CreateAsync([FromBody] NewsItem newsItem)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdNewsItem = await newsItemRepository.AddAsync(newsItem);
                return CreatedAtAction(nameof(GetByIdAsync), new { id = createdNewsItem.Id }, createdNewsItem);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating news item");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 更新新闻项目
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<NewsItem>> UpdateAsync(string id, [FromBody] NewsItem newsItem)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != newsItem.Id)
                {
                    return BadRequest("ID mismatch");
                }

                var updatedNewsItem = await newsItemRepository.UpdateAsync(newsItem);
                return Ok(updatedNewsItem);
            }
            catch (InvalidOperationException)
            {
                return NotFound($"NewsItem with ID {id} not found");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating news item {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除新闻项目
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteAsync(string id)
        {
            try
            {
                var deleted = await newsItemRepository.DeleteAsync(id);
                if (!deleted)
                {
                    return NotFound($"NewsItem with ID {id} not found");
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting news item {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据公司名称获取新闻
        /// </summary>
        [HttpGet("company/{company}")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> GetByCompanyAsync(string company)
        {
            try
            {
                var newsItems = await newsItemRepository.GetByCompanyAsync(company);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items for company {Company}", company);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据类别获取新闻
        /// </summary>
        [HttpGet("category/{category}")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> GetByCategoryAsync(NewsCategory category)
        {
            try
            {
                var newsItems = await newsItemRepository.GetByCategoryAsync(category);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items for category {Category}", category);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据日期范围获取新闻
        /// </summary>
        [HttpGet("daterange")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> GetByDateRangeAsync(
            [FromQuery, Required] DateTime startDate,
            [FromQuery, Required] DateTime endDate)
        {
            try
            {
                if (startDate > endDate)
                {
                    return BadRequest("Start date must be before end date");
                }

                var newsItems = await newsItemRepository.GetByDateRangeAsync(startDate, endDate);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items for date range {StartDate} to {EndDate}", startDate, endDate);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 搜索新闻标题
        /// </summary>
        [HttpGet("search/title")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> SearchByTitleAsync([FromQuery, Required] string searchTerm)
        {
            try
            {
                var newsItems = await newsItemRepository.SearchByTitleAsync(searchTerm);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error searching news items by title with term {SearchTerm}", searchTerm);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 搜索新闻内容
        /// </summary>
        [HttpGet("search/content")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> SearchByContentAsync([FromQuery, Required] string searchTerm)
        {
            try
            {
                var newsItems = await newsItemRepository.SearchByContentAsync(searchTerm);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error searching news items by content with term {SearchTerm}", searchTerm);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据标签获取新闻
        /// </summary>
        [HttpGet("tags")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> GetByTagsAsync([FromQuery, Required] string[] tags)
        {
            try
            {
                var newsItems = await newsItemRepository.GetByTagsAsync(tags);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items by tags {Tags}", string.Join(", ", tags));
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取最新新闻
        /// </summary>
        [HttpGet("recent")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> GetRecentNewsAsync([FromQuery] int count = 10)
        {
            try
            {
                if (count <= 0 || count > 100)
                {
                    return BadRequest("Count must be between 1 and 100");
                }

                var newsItems = await newsItemRepository.GetRecentNewsAsync(count);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving recent news items");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取公司统计信息
        /// </summary>
        [HttpGet("statistics/companies")]
        public async Task<ActionResult<Dictionary<string, int>>> GetCompanyStatisticsAsync()
        {
            try
            {
                var statistics = await newsItemRepository.GetCompanyStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving company statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取类别统计信息
        /// </summary>
        [HttpGet("statistics/categories")]
        public async Task<ActionResult<Dictionary<NewsCategory, int>>> GetCategoryStatisticsAsync()
        {
            try
            {
                var statistics = await newsItemRepository.GetCategoryStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving category statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 批量创建新闻项目
        /// </summary>
        [HttpPost("batch")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> CreateBatchAsync([FromBody] IEnumerable<NewsItem> newsItems)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdNewsItems = await newsItemRepository.AddRangeAsync(newsItems);
                return Ok(createdNewsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating batch news items");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
