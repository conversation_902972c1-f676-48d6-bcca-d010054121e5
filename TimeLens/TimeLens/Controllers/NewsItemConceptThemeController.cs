using Microsoft.AspNetCore.Mvc;
using TimeLens.Client.Data.Repositories;
using TimeLens.Client.Models;
using System.ComponentModel.DataAnnotations;

namespace TimeLens.Controllers
{
    /// <summary>
    /// NewsItemConceptTheme API控制器，提供RESTful API接口
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class NewsItemConceptThemeController(
        INewsItemConceptThemeRepository newsItemConceptThemeRepository,
        ILogger<NewsItemConceptThemeController> logger)
        : ControllerBase
    {
        /// <summary>
        /// 获取所有新闻概念主题关联
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<NewsItemConceptTheme>>> GetAllAsync()
        {
            try
            {
                var links = await newsItemConceptThemeRepository.GetAllAsync();
                return Ok(links);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving all news item concept theme links");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据ID获取新闻概念主题关联
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<NewsItemConceptTheme>> GetByIdAsync(string id)
        {
            try
            {
                var link = await newsItemConceptThemeRepository.GetByIdAsync(id);
                if (link == null)
                {
                    return NotFound($"NewsItemConceptTheme with ID {id} not found");
                }
                return Ok(link);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news item concept theme link {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 创建新的新闻概念主题关联
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<NewsItemConceptTheme>> CreateAsync([FromBody] NewsItemConceptTheme newsItemConceptTheme)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var created = await newsItemConceptThemeRepository.AddAsync(newsItemConceptTheme);
                return CreatedAtAction(nameof(GetByIdAsync), new { id = created.Id }, created);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating news item concept theme link");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 更新新闻概念主题关联
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<NewsItemConceptTheme>> UpdateAsync(string id, [FromBody] NewsItemConceptTheme newsItemConceptTheme)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != newsItemConceptTheme.Id)
                {
                    return BadRequest("ID mismatch");
                }

                var updated = await newsItemConceptThemeRepository.UpdateAsync(newsItemConceptTheme);
                return Ok(updated);
            }
            catch (InvalidOperationException)
            {
                return NotFound($"NewsItemConceptTheme with ID {id} not found");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating news item concept theme link {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除新闻概念主题关联
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> DeleteAsync(string id)
        {
            try
            {
                var result = await newsItemConceptThemeRepository.DeleteAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error deleting news item concept theme link {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 批量根据新闻ID获取概念主题
        /// </summary>
        [HttpPost("newsitems/batch")]
        public async Task<ActionResult<Dictionary<string, List<ConceptTheme>>>> GetConceptThemesByNewsItemIdsAsync([FromBody] IEnumerable<string> newsItemIds)
        {
            try
            {
                var result = await newsItemConceptThemeRepository.GetConceptThemesByNewsItemIdsAsync(newsItemIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept themes for multiple news items");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 批量根据概念主题ID获取新闻
        /// </summary>
        [HttpPost("conceptthemes/batch")]
        public async Task<ActionResult<Dictionary<string, List<NewsItem>>>> GetNewsItemsByConceptThemeIdsAsync([FromBody] IEnumerable<string> conceptThemeIds)
        {
            try
            {
                var result = await newsItemConceptThemeRepository.GetNewsItemsByConceptThemeIdsAsync(conceptThemeIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items for multiple concept themes");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 检查新闻和概念主题是否已关联
        /// </summary>
        [HttpGet("linked/newsitem/{newsItemId}/concepttheme/{conceptThemeId}")]
        public async Task<ActionResult<bool>> IsNewsItemConceptThemeLinkedAsync(string newsItemId, string conceptThemeId)
        {
            try
            {
                var isLinked = await newsItemConceptThemeRepository.IsNewsItemConceptThemeLinkedAsync(newsItemId, conceptThemeId);
                return Ok(isLinked);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error checking link between news item {NewsItemId} and concept theme {ConceptThemeId}", newsItemId, conceptThemeId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 创建新闻和概念主题的关联
        /// </summary>
        [HttpPost("link")]
        public async Task<ActionResult> AddNewsItemConceptThemeAsync([FromBody] LinkRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                await newsItemConceptThemeRepository.AddNewsItemConceptThemeAsync(request.NewsItemId, request.ConceptThemeId);
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error linking news item {NewsItemId} to concept theme {ConceptThemeId}", request.NewsItemId, request.ConceptThemeId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除新闻和概念主题的关联
        /// </summary>
        [HttpDelete("unlink/newsitem/{newsItemId}/concepttheme/{conceptThemeId}")]
        public async Task<ActionResult> RemoveNewsItemConceptThemeAsync(string newsItemId, string conceptThemeId)
        {
            try
            {
                await newsItemConceptThemeRepository.RemoveNewsItemConceptThemeAsync(newsItemId, conceptThemeId);
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error unlinking news item {NewsItemId} from concept theme {ConceptThemeId}", newsItemId, conceptThemeId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据新闻ID获取相关的概念主题
        /// </summary>
        [HttpGet("newsitem/{newsItemId}/conceptthemes")]
        public async Task<ActionResult<IEnumerable<ConceptTheme>>> GetConceptThemesByNewsItemIdAsync(string newsItemId)
        {
            try
            {
                var conceptThemes = await newsItemConceptThemeRepository.GetConceptThemesByNewsItemIdAsync(newsItemId);
                return Ok(conceptThemes);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept themes for news item {NewsItemId}", newsItemId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据概念主题ID获取相关的新闻
        /// </summary>
        [HttpGet("concepttheme/{conceptThemeId}/newsitems")]
        public async Task<ActionResult<IEnumerable<NewsItem>>> GetNewsItemsByConceptThemeIdAsync(string conceptThemeId)
        {
            try
            {
                var newsItems = await newsItemConceptThemeRepository.GetNewsItemsByConceptThemeIdAsync(conceptThemeId);
                return Ok(newsItems);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news items for concept theme {ConceptThemeId}", conceptThemeId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取概念主题关联统计
        /// </summary>
        [HttpGet("statistics/concepttheme-counts")]
        public async Task<ActionResult<Dictionary<string, int>>> GetConceptThemeNewsCountStatisticsAsync()
        {
            try
            {
                var statistics = await newsItemConceptThemeRepository.GetConceptThemeNewsCountStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept theme news count statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取最受欢迎的概念主题
        /// </summary>
        [HttpGet("popular")]
        public async Task<ActionResult<IEnumerable<object>>> GetMostPopularConceptThemesAsync([FromQuery] int topCount = 10)
        {
            try
            {
                var result = await newsItemConceptThemeRepository.GetMostPopularConceptThemesAsync(topCount);
                return Ok(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving most popular concept themes");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除指定新闻的所有关联
        /// </summary>
        [HttpDelete("newsitem/{newsItemId}")]
        public async Task<ActionResult> RemoveAllConceptThemesFromNewsItemAsync(string newsItemId)
        {
            try
            {
                await newsItemConceptThemeRepository.RemoveAllConceptThemesFromNewsItemAsync(newsItemId);
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error removing all concept themes from news item {NewsItemId}", newsItemId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除指定概念主题的所有关联
        /// </summary>
        [HttpDelete("concepttheme/{conceptThemeId}")]
        public async Task<ActionResult> RemoveAllNewsItemsFromConceptThemeAsync(string conceptThemeId)
        {
            try
            {
                await newsItemConceptThemeRepository.RemoveAllNewsItemsFromConceptThemeAsync(conceptThemeId);
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error removing all news items from concept theme {ConceptThemeId}", conceptThemeId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 批量创建关联
        /// </summary>
        [HttpPost("batch")]
        public async Task<ActionResult<IEnumerable<NewsItemConceptTheme>>> CreateBatchAsync([FromBody] IEnumerable<NewsItemConceptTheme> newsItemConceptThemes)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var created = await newsItemConceptThemeRepository.AddRangeAsync(newsItemConceptThemes);
                return Ok(created);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating batch news item concept theme links");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取新闻的概念主题数量
        /// </summary>
        [HttpGet("count/newsitem/{newsItemId}")]
        public async Task<ActionResult<int>> GetConceptThemeCountByNewsItemIdAsync(string newsItemId)
        {
            try
            {
                var count = await newsItemConceptThemeRepository.GetConceptThemeCountByNewsItemIdAsync(newsItemId);
                return Ok(count);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving concept theme count for news item {NewsItemId}", newsItemId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 获取概念主题的新闻数量
        /// </summary>
        [HttpGet("count/concepttheme/{conceptThemeId}")]
        public async Task<ActionResult<int>> GetNewsItemCountByConceptThemeIdAsync(string conceptThemeId)
        {
            try
            {
                var count = await newsItemConceptThemeRepository.GetNewsItemCountByConceptThemeIdAsync(conceptThemeId);
                return Ok(count);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving news item count for concept theme {ConceptThemeId}", conceptThemeId);
                return StatusCode(500, "Internal server error");
            }
        }
    }

    /// <summary>
    /// 关联请求模型
    /// </summary>
    public class LinkRequest
    {
        public required string NewsItemId { get; set; }
        public required string ConceptThemeId { get; set; }
    }
}
