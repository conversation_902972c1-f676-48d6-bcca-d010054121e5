using System.Text.Json;
using System.Text.Json.Serialization;
using Coravel;
using TimeLens.Components;
using TimeLens.Client.Services;
using Refit;
using TimeLens.Client.Tasks;
using TimeLens.Client.Utils;
using TimeLens.Client.Data.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();

builder.Services.AddAntDesign();
builder.Services.AddHttpClient();
builder.Services.AddScheduler();
builder.Services.AddTransient<FinanceSinaNewsTask>();

// Add Enhanced Data Access Services
if (builder.Environment.IsDevelopment())
{
    // builder.Services.AddDevelopmentServices();
    builder.Services.AddProductionServices(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "timelens_test.db"));
}
else
{
    builder.Services.AddProductionServices(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "timelens_production.db"));
}

// Add API Controllers for WebAssembly client support
builder.Services.AddControllers();

var options = new JsonSerializerOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
};
options.Converters.Add(new ObjectToInferredTypesConverter());
options.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase));
// Add Services
builder.Services.AddRefitClient<ISinaFeedApi>(new RefitSettings()
    {
        ContentSerializer = new SystemTextJsonContentSerializer(options),
        UrlParameterKeyFormatter = new SnakeCaseUrlParameterKeyFormatter()
    })
    .ConfigureHttpClient(c => {
        c.BaseAddress = new Uri("https://zhibo.sina.com.cn");
        c.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        c.DefaultRequestHeaders.Referrer = new Uri("https://zhibo.sina.com.cn/");
        c.DefaultRequestHeaders.Host = "zhibo.sina.com.cn";
        c.Timeout = TimeSpan.FromSeconds(20);
    }).AddHttpMessageHandler(serviceProvider
        => new HttpInterceptorLogger(serviceProvider.GetRequiredService<ILogger<HttpInterceptorLogger>>()));
builder.Services.AddSingleton<HttpInterceptorLogger>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseAntiforgery();
app.UseStaticFiles();
app.MapStaticAssets();

// Map API controllers
app.MapControllers();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(TimeLens.Client._Imports).Assembly);

// app.Services.UseScheduler(scheduler => {
//     scheduler
//         .Schedule<FinanceSinaNewsTask>()
//         .EveryMinute(); // 每1分钟执行
// });
app.Run();
