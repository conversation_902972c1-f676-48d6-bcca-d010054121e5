using TimeLens.Client.Data.Context;
using TimeLens.Client.Data.Repositories;
using TimeLens.Client.Models;

namespace TimeLens.Tests.Data
{
    /// <summary>
    /// NewsItem Repository单元测试
    /// </summary>
    public class NewsItemRepositoryTests : IDisposable
    {
        private readonly IEnhancedDuckDbContext _context;
        private readonly EnhancedNewsItemRepository _repository;

        public NewsItemRepositoryTests()
        {
            // 使用内存数据库进行测试
            _context = new EnhancedDuckDbContext("Data Source=:memory:");
            INewsItemConceptThemeRepository relationRepository = new NewsItemConceptThemeRepository(_context);
            _repository = new EnhancedNewsItemRepository(_context, relationRepository);
        }

        [Fact]
        public async Task AddAsync_ShouldCreateNewsItem_WhenValidDataProvided()
        {
            // Arrange
            var newsItem = CreateTestNewsItem();

            // Act
            var result = await _repository.AddAsync(newsItem);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(newsItem.Title, result.Title);
            Assert.Equal(newsItem.Company, result.Company);
            Assert.NotEqual(default(DateTime), result.CreatedAt);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnNewsItem_WhenItemExists()
        {
            // Arrange
            var newsItem = CreateTestNewsItem();
            await _repository.AddAsync(newsItem);

            // Act
            var result = await _repository.GetByIdAsync(newsItem.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(newsItem.Id, result.Id);
            Assert.Equal(newsItem.Title, result.Title);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnNull_WhenItemDoesNotExist()
        {
            // Act
            var result = await _repository.GetByIdAsync("non-existent-id");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task UpdateAsync_ShouldModifyNewsItem_WhenValidDataProvided()
        {
            // Arrange
            var newsItem = CreateTestNewsItem();
            await _repository.AddAsync(newsItem);

            // Create a new instance with updated values since Title and Summary are init-only
            var updatedNewsItem = new NewsItem
            {
                Id = newsItem.Id,
                Title = "Updated Title",
                Content = newsItem.Content,
                PublishDate = newsItem.PublishDate,
                Company = newsItem.Company,
                Category = newsItem.Category,
                Summary = "Updated Summary",
                Source = newsItem.Source,
                Importance = ImportanceCategory.High,
                Tags = newsItem.Tags,
                ConceptThemes = newsItem.ConceptThemes
            };

            // Act
            var result = await _repository.UpdateAsync(updatedNewsItem);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Updated Title", result.Title);
            Assert.Equal("Updated Summary", result.Summary);
            Assert.True(result.UpdatedAt > result.CreatedAt);
        }

        [Fact]
        public async Task DeleteAsync_ShouldRemoveNewsItem_WhenItemExists()
        {
            // Arrange
            var newsItem = CreateTestNewsItem();
            await _repository.AddAsync(newsItem);

            // Act
            var result = await _repository.DeleteAsync(newsItem.Id);

            // Assert
            Assert.True(result);

            // Verify item is deleted
            var deletedItem = await _repository.GetByIdAsync(newsItem.Id);
            Assert.Null(deletedItem);
        }

        [Fact]
        public async Task GetByCompanyAsync_ShouldReturnFilteredResults()
        {
            // Arrange
            var newsItem1 = CreateTestNewsItem("Company A");
            var newsItem2 = CreateTestNewsItem("Company B");
            var newsItem3 = CreateTestNewsItem("Company A");

            await _repository.AddAsync(newsItem1);
            await _repository.AddAsync(newsItem2);
            await _repository.AddAsync(newsItem3);

            // Act
            var results = await _repository.GetByCompanyAsync("Company A");

            // Assert
            var newsItems = results as NewsItem[] ?? results.ToArray();
            if (newsItems != null)
            {
                Assert.Equal(2, newsItems.Length);
                Assert.All(newsItems, item => Assert.Equal("Company A", item.Company));
            }
        }

        [Fact]
        public async Task GetByCategoryAsync_ShouldReturnFilteredResults()
        {
            // Arrange
            var newsItem1 = CreateTestNewsItem(category: NewsCategory.年报);
            var newsItem2 = CreateTestNewsItem(category: NewsCategory.分红);
            var newsItem3 = CreateTestNewsItem(category: NewsCategory.年报);

            await _repository.AddAsync(newsItem1);
            await _repository.AddAsync(newsItem2);
            await _repository.AddAsync(newsItem3);

            // Act
            var results = await _repository.GetByCategoryAsync(NewsCategory.年报);

            // Assert
            var newsItems = results as NewsItem[] ?? results.ToArray();
            Assert.Equal(2, newsItems.Length);
            Assert.All(newsItems, item => Assert.Equal(NewsCategory.年报, item.Category));
        }

        [Fact]
        public async Task SearchByTitleAsync_ShouldReturnMatchingResults()
        {
            // Arrange
            var newsItem1 = CreateTestNewsItem(title: "Important Company News");
            var newsItem2 = CreateTestNewsItem(title: "Market Update");
            var newsItem3 = CreateTestNewsItem(title: "Company Announcement");

            await _repository.AddAsync(newsItem1);
            await _repository.AddAsync(newsItem2);
            await _repository.AddAsync(newsItem3);

            // Act
            var results = await _repository.SearchByTitleAsync("Company");

            // Assert
            var newsItems = results as NewsItem[] ?? results.ToArray();
            Assert.Equal(2, newsItems.Length);
            Assert.All(newsItems, item => Assert.Contains("Company", item.Title));
        }

        [Fact]
        public async Task CountAsync_ShouldReturnCorrectCount()
        {
            // Arrange
            await _repository.AddAsync(CreateTestNewsItem("Company A"));
            await _repository.AddAsync(CreateTestNewsItem("Company B"));
            await _repository.AddAsync(CreateTestNewsItem("Company A"));

            // Act
            var totalCount = await _repository.CountAsync();
            var companyACount = await _repository.CountAsync(x => x.Company == "Company A");

            // Assert
            Assert.Equal(3, totalCount);
            Assert.Equal(2, companyACount);
        }

        [Fact]
        public async Task GetRecentNewsAsync_ShouldReturnLimitedResults()
        {
            // Arrange
            for (int i = 0; i < 15; i++)
            {
                var newsItem = CreateTestNewsItem($"Company {i}", publishDate: DateTime.UtcNow.AddDays(-i));
                await _repository.AddAsync(newsItem);
            }

            // Act
            var results = await _repository.GetRecentNewsAsync();

            // Assert
            var newsItems = results as NewsItem[] ?? results.ToArray();
            Assert.Equal(10, newsItems.Length);
            
            // Verify they are ordered by publish date descending
            var resultsList = newsItems.ToList();
            for (int i = 0; i < resultsList.Count - 1; i++)
            {
                Assert.True(resultsList[i].PublishDate >= resultsList[i + 1].PublishDate);
            }
        }

        [Fact]
        public async Task AddRangeAsync_ShouldCreateMultipleItems()
        {
            // Arrange
            var newsItems = new List<NewsItem>
            {
                CreateTestNewsItem("Company A"),
                CreateTestNewsItem("Company B"),
                CreateTestNewsItem("Company C")
            };

            // Act
            var results = await _repository.AddRangeAsync(newsItems);

            // Assert
            Assert.Equal(3, results.Count());
            
            var totalCount = await _repository.CountAsync();
            Assert.Equal(3, totalCount);
        }

        private NewsItem CreateTestNewsItem(string company = "Test Company", string title = "Test News", NewsCategory category = NewsCategory.其他, DateTime? publishDate = null)
        {
            return new NewsItem
            {
                Title = title,
                Content = "This is test news content for unit testing purposes.",
                Company = company,
                Category = category,
                Summary = "Test news summary",
                Source = "Unit Test",
                Importance = ImportanceCategory.Low,
                PublishDate = publishDate ?? DateTime.UtcNow,
                Tags = ["test", "unit-test"],
                ConceptThemes =
                [
                    new ConceptTheme
                    {
                        Name = "Test Theme",
                        Heat = 50,
                        ActiveStocks = ["TEST001", "TEST002"]
                    }
                ]
            };
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
